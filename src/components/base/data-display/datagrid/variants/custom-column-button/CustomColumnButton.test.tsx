import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import '@testing-library/jest-dom';
import { DataGridPremium, Toolbar } from '@mui/x-data-grid-premium';
import { type FC } from 'react';
import CustomColumnButton from './CustomColumnButton';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'common.action.selectColumns': 'select columns',
        'common.label.columns': 'columns',
      };
      return translations[key] ?? key;
    },
  }),
}));

// Mock transformText utility
vi.mock('@/utils/text-transform.util', () => ({
  transformText: vi.fn((text: string, transform: string) => {
    if (transform === 'sentenceCase') {
      return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
    }
    return text;
  }),
}));

// Mock Button component
vi.mock('@/components/base/inputs/button', () => ({
  Button: vi.fn(({ children, title, startIcon, ...props }) => (
    <button
      data-testid="custom-button"
      title={title}
      aria-label={title}
      {...props}
    >
      {startIcon && <span data-testid="start-icon">{startIcon}</span>}
      {children}
    </button>
  )),
}));

// Mock MUI icons
vi.mock('@mui/icons-material/ViewColumn', () => ({
  __esModule: true,
  default: () => <span data-testid="view-column-icon">ViewColumn</span>,
}));

// Mock MUI DataGrid components
vi.mock('@mui/x-data-grid-premium', async () => {
  const actual = await vi.importActual('@mui/x-data-grid-premium');

  return {
    ...actual,
    ColumnsPanelTrigger: vi.fn(({ render }) => (
      <div data-testid="columns-panel-trigger">{render}</div>
    )),
    ToolbarButton: vi.fn(({ render }) => <div data-testid="toolbar-button">{render}</div>),
  };
});

// Test wrapper component
const CustomToolbarComponent: FC = () => (
  <Toolbar>
    <CustomColumnButton />
  </Toolbar>
);

describe('CustomColumnButton', () => {
  let TestComponent: FC;

  beforeEach(() => {
    vi.clearAllMocks();

    TestComponent = () => {
      return (
        <DataGridPremium
          columns={[{ field: 'id', headerName: 'ID' }]}
          showToolbar
          slots={{
            toolbar: CustomToolbarComponent,
          }}
        />
      );
    };
  });

  it('renders correctly with all required components', () => {
    render(<TestComponent />);

    // Check that the main components are rendered
    expect(screen.getByTestId('columns-panel-trigger')).toBeInTheDocument();
    expect(screen.getByTestId('toolbar-button')).toBeInTheDocument();
    expect(screen.getByTestId('custom-button')).toBeInTheDocument();
  });

  it('renders button with correct props', () => {
    render(<TestComponent />);

    const button = screen.getByTestId('custom-button');

    // Check button properties
    expect(button).toHaveAttribute('title', 'Select columns');
    expect(button).toHaveAttribute('aria-label', 'Select columns');
    expect(button).toHaveTextContent('columns');
  });

  it('renders ViewColumn icon as start icon', () => {
    render(<TestComponent />);

    const startIcon = screen.getByTestId('start-icon');
    const viewColumnIcon = screen.getByTestId('view-column-icon');

    expect(startIcon).toBeInTheDocument();
    expect(viewColumnIcon).toBeInTheDocument();
    expect(viewColumnIcon).toHaveTextContent('ViewColumn');
  });

  it('uses correct translation keys', async () => {
    const { transformText } = await import('@/utils/text-transform.util');

    render(<TestComponent />);

    // Verify transformText was called with correct parameters
    expect(vi.mocked(transformText)).toHaveBeenCalledWith('select columns', 'sentenceCase');
  });

  it('renders without crashing when used standalone', () => {
    // Test the component in isolation
    const StandaloneComponent = () => <CustomColumnButton />;

    expect(() => render(<StandaloneComponent />)).not.toThrow();
  });
});
